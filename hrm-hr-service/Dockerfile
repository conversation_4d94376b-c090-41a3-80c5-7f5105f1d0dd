# Stage 1: build with wrapper & cache dependencies
FROM eclipse-temurin:17-jdk AS build

WORKDIR /app

# Cache dependencies
RUN ./gradlew dependencies

# Copy wrapper & build config trước để cache dependency
COPY .. .


# Build JAR
RUN ./gradlew build -x test

# Stage 2: run app
FROM eclipse-temurin:17-jdk

WORKDIR /app

COPY --from=build /app/build/libs/*.jar app.jar

EXPOSE 6020

ENTRYPOINT ["java", "-jar", "app.jar"]
